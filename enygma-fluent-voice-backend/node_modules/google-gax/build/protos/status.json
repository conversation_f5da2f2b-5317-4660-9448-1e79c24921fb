{"nested": {"google": {"nested": {"protobuf": {"nested": {"Any": {"fields": {"type_url": {"type": "string", "id": 1}, "value": {"type": "bytes", "id": 2}}}, "Duration": {"fields": {"seconds": {"type": "int64", "id": 1}, "nanos": {"type": "int32", "id": 2}}}}}, "rpc": {"options": {"cc_enable_arenas": true, "go_package": "google.golang.org/genproto/googleapis/rpc/errdetails;errdetails", "java_multiple_files": true, "java_outer_classname": "ErrorDetailsProto", "java_package": "com.google.rpc", "objc_class_prefix": "RPC"}, "nested": {"Status": {"fields": {"code": {"type": "int32", "id": 1}, "message": {"type": "string", "id": 2}, "details": {"rule": "repeated", "type": "google.protobuf.Any", "id": 3}}}, "RetryInfo": {"fields": {"retryDelay": {"type": "google.protobuf.Duration", "id": 1}}}, "DebugInfo": {"fields": {"stackEntries": {"rule": "repeated", "type": "string", "id": 1}, "detail": {"type": "string", "id": 2}}}, "QuotaFailure": {"fields": {"violations": {"rule": "repeated", "type": "Violation", "id": 1}}, "nested": {"Violation": {"fields": {"subject": {"type": "string", "id": 1}, "description": {"type": "string", "id": 2}}}}}, "ErrorInfo": {"fields": {"reason": {"type": "string", "id": 1}, "domain": {"type": "string", "id": 2}, "metadata": {"keyType": "string", "type": "string", "id": 3}}}, "PreconditionFailure": {"fields": {"violations": {"rule": "repeated", "type": "Violation", "id": 1}}, "nested": {"Violation": {"fields": {"type": {"type": "string", "id": 1}, "subject": {"type": "string", "id": 2}, "description": {"type": "string", "id": 3}}}}}, "BadRequest": {"fields": {"fieldViolations": {"rule": "repeated", "type": "FieldViolation", "id": 1}}, "nested": {"FieldViolation": {"fields": {"field": {"type": "string", "id": 1}, "description": {"type": "string", "id": 2}}}}}, "RequestInfo": {"fields": {"requestId": {"type": "string", "id": 1}, "servingData": {"type": "string", "id": 2}}}, "ResourceInfo": {"fields": {"resourceType": {"type": "string", "id": 1}, "resourceName": {"type": "string", "id": 2}, "owner": {"type": "string", "id": 3}, "description": {"type": "string", "id": 4}}}, "Help": {"fields": {"links": {"rule": "repeated", "type": "Link", "id": 1}}, "nested": {"Link": {"fields": {"description": {"type": "string", "id": 1}, "url": {"type": "string", "id": 2}}}}}, "LocalizedMessage": {"fields": {"locale": {"type": "string", "id": 1}, "message": {"type": "string", "id": 2}}}}}}}}}