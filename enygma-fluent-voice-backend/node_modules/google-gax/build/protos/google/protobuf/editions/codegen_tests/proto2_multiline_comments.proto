syntax = "proto2";

package protobuf_editions_test.proto2;

/**
Multiline message comment - no asterisk
*/
message Message1 {
  /**
  Multiline field comment - no asterisk
  */
  optional string field = 1;
}

/*
 * Multiline message comment - single asterisk
 */
message Message2 {
  /*
   * Multiline message comment - single asterisk
   */
  optional string field = 1;
}

/**
 * Exactly one trait must be set. Extension # is vendor_id + 1.
 */
message Message3 {
  /**
   * Exactly one trait must be set. Extension # is vendor_id + 1.
   */
  optional string field = 1;
}
