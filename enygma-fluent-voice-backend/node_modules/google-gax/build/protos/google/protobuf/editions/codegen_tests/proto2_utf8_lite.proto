// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto2";

package protobuf_editions_test.proto2;

option optimize_for = LITE_RUNTIME;

message Proto2Utf8Lite {
  optional string string_field = 1;
  map<string, string> map_field = 2;
}
