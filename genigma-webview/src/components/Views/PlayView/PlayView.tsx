import { useState, useEffect, useRef } from "react";
import { Image, Mic, Modal } from "microapps";
// import { io, Socket } from "socket.io-client"; // Comentado - no usamos backend externo
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { useQuestionsColor } from "../../../utils/questionsColorSystem";
import { cluesStorage } from "../../../services/ai/storage/CluesStorage";
import "./PlayView.scss";

// Declaraciones de tipos para Web Speech API
interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

interface SpeechRecognitionResult {
  isFinal: boolean;
  0: SpeechRecognitionAlternative;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognitionResultList {
  length: number;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  start(): void;
  stop(): void;
}

declare global {
  interface Window {
    SpeechRecognition: {
      new (): SpeechRecognition;
    };
    webkitSpeechRecognition: {
      new (): SpeechRecognition;
    };
  }
}

interface CountdownMessages {
  questionsCountdownMessages: Record<string, string>;
}

interface PlayViewProps {
  handleShowClues: () => void;
  handleExistGame: () => void;
  showExitPopup: boolean;
  handleConfirmExit: () => void;
  handleCancelExit: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({
  handleShowClues,
  handleExistGame,
  showExitPopup,
  handleConfirmExit,
  handleCancelExit,
}) => {
  const { session, askQuestion, askInitialMessage, questionsRemaining } = useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  // Variables de input de texto comentadas - usando funcionalidad de voz en tiempo real
  // const [inputText, setInputText] = useState("");
  const [isLivesPopUpShown, setIsLivesPopUpShown] = useState(false);
  const [showClueTooltip, setShowClueTooltip] = useState(false);
  const [currentClue, setCurrentClue] = useState("");
  const [isClueButtonVibrating, setIsClueButtonVibrating] = useState(false);
  const [isClueFadingOut, setIsClueFadingOut] = useState(false);
  const [lastShownClueId, setLastShownClueId] = useState<string | null>(null);
  const [countdownMessages, setCountdownMessages] = useState<CountdownMessages | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Hook para obtener el color basado en preguntas restantes
  const questionsColorData = useQuestionsColor(
    session?.maxQuestions || 20,
    session?.questionCount || 0
  );

  // Cargar mensajes de cuenta regresiva
  useEffect(() => {
    const loadCountdownMessages = async () => {
      try {
        const response = await fetch('/questions-countdown-messages.json');
        const data: CountdownMessages = await response.json();
        setCountdownMessages(data);
      } catch (error) {
        console.error('Error loading countdown messages:', error);
      }
    };

    loadCountdownMessages();
  }, []);

  // Función para obtener el mensaje de cuenta regresiva
  const getCountdownMessage = (remainingQuestions: number): string | null => {
    if (!countdownMessages) return null;

    const messageKey = remainingQuestions.toString();
    return countdownMessages.questionsCountdownMessages[messageKey] || null;
  };

  // Voice states - Web Speech API
  const [isListening, setIsListening] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [currentTranscript, setCurrentTranscript] = useState("");
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);
  const [isVoiceInitialized, setIsVoiceInitialized] = useState(false);
  const [isAIResponding, setIsAIResponding] = useState(false);

  // References
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const silenceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const restartTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Web Speech API functions
  const initializeSpeechRecognition = () => {
    console.log("🔧 Inicializando Speech Recognition...");

    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.log("❌ Navegador no soporta reconocimiento de voz");
      setVoiceError("Tu navegador no soporta reconocimiento de voz");
      return false;
    }

    console.log("✅ Navegador soporta reconocimiento de voz");
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    console.log("🎤 Objeto SpeechRecognition creado");

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'es-ES';
    recognition.maxAlternatives = 1;

    recognition.onstart = () => {
      setIsListening(true);
      setVoiceError(null);
      console.log("🎤 Reconocimiento de voz iniciado");
    };

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      // Mostrar transcripción en tiempo real
      setCurrentTranscript(interimTranscript || finalTranscript);

      // Si hay una transcripción final, procesarla
      if (finalTranscript.trim()) {
        handleVoiceInput(finalTranscript.trim());
      }
    };

    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      console.error("Error en reconocimiento de voz:", event.error);
      setIsListening(false);

      // Limpiar cualquier timer de reinicio pendiente
      if (restartTimerRef.current) {
        clearTimeout(restartTimerRef.current);
        restartTimerRef.current = null;
      }

      // Manejar diferentes tipos de errores
      switch (event.error) {
        case 'no-speech':
          // No es realmente un error, solo significa que no detectó habla
          console.log("🔇 No se detectó habla, reintentando en 2 segundos...");
          // Reintentar automáticamente después de un delay más largo
          restartTimerRef.current = setTimeout(() => {
            if (!isProcessingVoice && !voiceError) {
              startListening();
            }
          }, 2000);
          break;
        case 'audio-capture':
          setVoiceError("No se puede acceder al micrófono. Verifica los permisos.");
          break;
        case 'not-allowed':
          setVoiceError("Permisos de micrófono denegados. Permite el acceso al micrófono.");
          break;
        case 'network':
          setVoiceError("Error de conexión. Verifica tu conexión a internet.");
          break;
        case 'aborted':
          // El reconocimiento fue abortado intencionalmente, no mostrar error
          console.log("🔇 Reconocimiento abortado intencionalmente");
          break;
        default:
          setVoiceError(`Error: ${event.error}`);
          break;
      }
    };

    recognition.onend = () => {
      console.log("🔇 Reconocimiento de voz terminado");
      setIsListening(false);
      setCurrentTranscript("");

      // Solo reiniciar automáticamente si no hay errores y no estamos procesando
      // y no hay un timer de reinicio ya programado
      if (!isProcessingVoice && !voiceError && !restartTimerRef.current) {
        restartTimerRef.current = setTimeout(() => {
          restartTimerRef.current = null;
          if (!isProcessingVoice && !voiceError) {
            startListening();
          }
        }, 1000);
      }
    };

    recognitionRef.current = recognition;
    console.log("✅ SpeechRecognition configurado y guardado en ref");
    return true;
  };

  const handleVoiceInput = async (transcript: string) => {
    console.log("🗣️ Transcripción recibida:", transcript);

    // Filtrar transcripciones muy cortas o vacías
    if (transcript.length < 3) {
      console.log("🔇 Transcripción muy corta, ignorando...");
      return;
    }

    // Limpiar el timer de silencio si existe
    if (silenceTimerRef.current) {
      clearTimeout(silenceTimerRef.current);
      silenceTimerRef.current = null;
    }

    // Detener el reconocimiento temporalmente
    console.log("🛑 Deteniendo reconocimiento para procesar pregunta");
    stopListening();

    // Procesar la pregunta usando el sistema existente
    setIsProcessingVoice(true);
    setIsLoading(true);
    setCurrentTranscript(""); // Limpiar la transcripción mostrada

    try {
      console.log("📤 Enviando pregunta al LLM:", transcript);
      await askQuestion(transcript);
      console.log("✅ Pregunta enviada exitosamente");
    } catch (error) {
      console.error("❌ Error enviando pregunta por voz:", error);
      setVoiceError("Error al procesar la pregunta");
    } finally {
      setIsProcessingVoice(false);
      setIsLoading(false);

      // Reiniciar el reconocimiento después de un delay más largo
      // para dar tiempo a que termine la respuesta de la IA
      setTimeout(() => {
        if (!voiceError) {
          console.log("🔄 Reiniciando reconocimiento de voz después de respuesta...");
          startListening();
        }
      }, 5000); // Delay de 5 segundos para dar tiempo a la respuesta completa
    }
  };

  const startListening = () => {
    // No iniciar si ya está escuchando, procesando, o la IA está respondiendo
    if (isListening || isProcessingVoice || isAIResponding) {
      console.log("🚫 No se puede iniciar: isListening =", isListening, "isProcessingVoice =", isProcessingVoice, "isAIResponding =", isAIResponding);
      return;
    }

    // Limpiar cualquier timer de reinicio pendiente
    if (restartTimerRef.current) {
      clearTimeout(restartTimerRef.current);
      restartTimerRef.current = null;
    }

    if (!recognitionRef.current) {
      if (!initializeSpeechRecognition()) {
        return;
      }
    }

    try {
      // Limpiar cualquier error previo
      setVoiceError(null);
      recognitionRef.current?.start();
      console.log("🎤 Intentando iniciar reconocimiento de voz...");
    } catch (error: any) {
      console.error("Error iniciando reconocimiento:", error);

      // Si el reconocimiento ya está activo, detenerlo primero
      if (error.name === 'InvalidStateError') {
        console.log("🔄 Reconocimiento ya activo, esperando...");
        // No reiniciar inmediatamente, esperar a que termine naturalmente
        return;
      } else {
        setVoiceError("Error al iniciar el micrófono");
      }
    }
  };

  const stopListening = () => {
    try {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        console.log("🛑 Deteniendo reconocimiento de voz...");
      }
    } catch (error) {
      console.error("Error deteniendo reconocimiento:", error);
    }

    setCurrentTranscript("");
    setIsListening(false);

    // Limpiar todos los timers
    if (silenceTimerRef.current) {
      clearTimeout(silenceTimerRef.current);
      silenceTimerRef.current = null;
    }

    if (restartTimerRef.current) {
      clearTimeout(restartTimerRef.current);
      restartTimerRef.current = null;
    }
  };

  const toggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      // Limpiar errores previos al reiniciar manualmente
      setVoiceError(null);
      startListening();
    }
  };

  const restartVoiceRecognition = () => {
    console.log("🔄 Reinicio manual del reconocimiento de voz");
    stopListening();
    setVoiceError(null);

    // Usar el timer de reinicio para evitar conflictos
    restartTimerRef.current = setTimeout(() => {
      restartTimerRef.current = null;
      startListening();
    }, 1000);
  };

  // Inicializar reconocimiento de voz al cargar el componente
  useEffect(() => {
    console.log("🔄 useEffect de voz - session messages length:", session?.messages?.length, "isVoiceInitialized:", isVoiceInitialized);

    // Solo inicializar una vez cuando hay mensajes y no se ha inicializado aún
    if (session && session.messages && session.messages.length > 0 && !isVoiceInitialized) {
      console.log("✅ Condiciones cumplidas, inicializando reconocimiento de voz INMEDIATAMENTE");
      setIsVoiceInitialized(true);

      // Inicializar inmediatamente para testing
      console.log("⏰ Inicializando reconocimiento...");
      if (initializeSpeechRecognition()) {
        console.log("🎤 Iniciando escucha...");
        startListening();
      } else {
        console.log("❌ Falló la inicialización del reconocimiento");
      }
    } else if (session?.messages?.length === 0) {
      console.log("⏳ Esperando mensaje inicial del juego...");
    }

    // Solo limpiar al desmontar completamente el componente
    return () => {
      if (!session) {
        console.log("🧹 Limpiando reconocimiento de voz al desmontar");
        stopListening();
        if (restartTimerRef.current) {
          clearTimeout(restartTimerRef.current);
          restartTimerRef.current = null;
        }
      }
    };
  }, [session?.messages?.length, isVoiceInitialized]);



  // Auto-scroll
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Monitorear pistas automáticamente cuando se generan nuevas
  useEffect(() => {
    if (session?.messages && session.messages.length > 0) {
      const lastMessage = session.messages[session.messages.length - 1];

      // Solo procesar mensajes de la IA
      if (lastMessage.sender === "ai") {
        try {
          // Obtener la pista más reciente usando CluesStorage
          const recentClues = cluesStorage.getRecent(1);

          if (recentClues.length > 0) {
            const latestClue = recentClues[0];

            // Verificar si es una pista nueva que no hemos mostrado
            if (latestClue.id !== lastShownClueId && latestClue.timestamp) {
              // Verificar si la pista es reciente (dentro de los últimos 10 segundos)
              const clueTime = new Date(latestClue.timestamp).getTime();
              const messageTime = new Date(lastMessage.timestamp).getTime();

              if (Math.abs(clueTime - messageTime) < 10000 && latestClue.text) {
                showClueWithAnimation(`💡 ${latestClue.text}`);
                setLastShownClueId(latestClue.id);
              }
            }
          }
        } catch (error) {
          console.error("Error procesando pistas automáticas:", error);
        }
      }
    }
  }, [session?.messages, lastShownClueId]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map(
        (msg: SessionMessage): ChatMessage => ({
          id: msg.id,
          text: msg.text,
          sender: msg.sender,
          timestamp: msg.timestamp,
        })
      );
      setMessages(chatMessages);

      // Detectar cuando la IA está respondiendo
      const lastMessage = session.messages[session.messages.length - 1];
      if (lastMessage && lastMessage.sender === "ai") {
        console.log("🤖 IA respondiendo, bloqueando micrófono temporalmente");
        setIsAIResponding(true);

        // Detener el reconocimiento si está activo
        if (isListening) {
          stopListening();
        }

        // Reactivar el micrófono después de un delay
        setTimeout(() => {
          console.log("✅ IA terminó de responder, reactivando micrófono");
          setIsAIResponding(false);
          if (isVoiceInitialized && !isProcessingVoice && !voiceError) {
            startListening();
          }
        }, 3000); // 3 segundos de delay después de la respuesta de la IA
      }
    }
  }, [session?.messages]);

  useEffect(() => {
    if (
      session &&
      (!session.messages || session.messages.length === 0) &&
      messages.length === 0
    ) {
      const sendInitialMessage = async () => {
        try {
          setIsLoading(true);
          await askInitialMessage("");
        } catch (error) {
          console.error("Error sending initial message:", error);
        } finally {
          setIsLoading(false);
        }
      };

      sendInitialMessage();
    }
  }, [session, messages.length, askInitialMessage]);

  // Funciones de input de texto comentadas - usando funcionalidad de voz en tiempo real
  /*
  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      await askQuestion(messageText);
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  */

  // Función para mostrar pista con animación
  const showClueWithAnimation = (clueText: string) => {
    console.log("🔍 Mostrando pista automática:", clueText);
    setCurrentClue(clueText);
    setShowClueTooltip(true);
    setIsClueFadingOut(false);
    setIsClueButtonVibrating(true);

    // Quitar vibración del botón después de 0.6s
    setTimeout(() => {
      setIsClueButtonVibrating(false);
    }, 600);

    // Iniciar fade out después de 4 segundos
    setTimeout(() => {
      setIsClueFadingOut(true);
    }, 4000);

    // Ocultar completamente después de 5 segundos
    setTimeout(() => {
      setShowClueTooltip(false);
      setIsClueFadingOut(false);
      setCurrentClue("");
    }, 5000);
  };

  return (
    <>
      <div className="chat-view">
        <div className="menu-left">
          <div className="enygma-logo">


            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <Mic
                level={isListening ? 50 : 0}
                onClick={toggleListening}
                state={
                  isAIResponding || isProcessingVoice
                    ? "disabled"
                    : isListening
                      ? "recording"
                      : voiceError
                        ? "disabled"
                        : "default"
                }
              />
              {voiceError && (
                <div
                  className="voice-error"
                  style={{ color: "red", fontSize: "12px", marginTop: "4px" }}
                >
                  {voiceError}
                </div>
              )}
              {isAIResponding && (
                <div
                  className="ai-responding"
                  style={{ color: "#88FFD5", fontSize: "12px", marginTop: "4px" }}
                >
                  🤖 IA respondiendo...
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="chat-view-wrapper">
          <div className="chat-container">
            <div
              className={`chat-content ${
                messages.length > 0 &&
                messages[messages.length - 1].sender === "user"
                  ? "align-right"
                  : "align-left"
              }`}
            >
              <div className="chat-text body1">
                {isLoading || isProcessingVoice
                  ? "Procesando..."
                  : isAIResponding
                    ? "🤖 IA respondiendo... (micrófono bloqueado)"
                    : messages.length > 0
                      ? messages[messages.length - 1].text
                      : isListening
                        ? "🎤 Escuchando... Habla ahora"
                        : "Haz clic en el micrófono para comenzar"}
              </div>
            </div>
          </div>

          <div className="chat-input-container">
            {/* Mensaje de cuenta regresiva */}
            {(() => {
              const countdownMessage = getCountdownMessage(questionsRemaining);
              return countdownMessage ? (
                <div className="countdown-message">
                  {countdownMessage}
                </div>
              ) : null;
            })()}

            {/* Input de texto comentado - funcionalidad de voz en tiempo real activa */}
            {/*
            <div className="input-wrapper">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Haz una pregunta sobre el personaje..."
                disabled={isLoading || !session}
                className="chat-input"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim() || isLoading || !session}
                className="send-button"
              >
                {isLoading ? "Enviando..." : "Enviar"}
              </button>
            </div>
            */}

            {/* Indicador de estado de voz */}
            <div className="voice-status-container" style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '1rem',
              padding: '1rem',
              background: 'rgba(136, 255, 213, 0.1)',
              borderRadius: '12px',
              border: '1px solid rgba(136, 255, 213, 0.3)'
            }}>
              <div style={{
                fontSize: '1.1rem',
                fontWeight: '600',
                color: isAIResponding ? '#ff6b6b' : isListening ? '#88FFD5' : '#666',
                textAlign: 'center'
              }}>
                {isAIResponding
                  ? '🚫 Micrófono bloqueado - IA respondiendo'
                  : isListening
                    ? '🎤 Escuchando... Habla ahora'
                    : '🔇 Micrófono desactivado - Haz clic en el micrófono para activar'
                }
              </div>

              {currentTranscript && (
                <div style={{
                  fontSize: '0.9rem',
                  color: '#88FFD5',
                  fontStyle: 'italic',
                  textAlign: 'center',
                  padding: '0.5rem',
                  background: 'rgba(136, 255, 213, 0.2)',
                  borderRadius: '8px'
                }}>
                  "🗣️ {currentTranscript}"
                </div>
              )}

              {(isLoading || isProcessingVoice) && (
                <div style={{
                  fontSize: '0.9rem',
                  color: '#88FFD5',
                  fontStyle: 'italic'
                }}>
                  🤖 Procesando tu pregunta...
                </div>
              )}

              {/* Botones de control de voz */}
              <div style={{
                display: 'flex',
                gap: '0.5rem',
                justifyContent: 'center',
                flexWrap: 'wrap'
              }}>
                <button
                  onClick={toggleListening}
                  style={{
                    padding: '0.5rem 1rem',
                    background: isListening ? '#ff6b6b' : '#88FFD5',
                    color: '#001428',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '0.8rem',
                    fontWeight: '600',
                    cursor: 'pointer'
                  }}
                >
                  {isListening ? '🛑 Detener' : '🎤 Iniciar Voz'}
                </button>

                {!isVoiceInitialized && (
                  <button
                    onClick={() => {
                      console.log("🔧 Inicialización manual");
                      if (initializeSpeechRecognition()) {
                        setIsVoiceInitialized(true);
                        startListening();
                      }
                    }}
                    style={{
                      padding: '0.5rem 1rem',
                      background: '#88FFD5',
                      color: '#001428',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '0.8rem',
                      fontWeight: '600',
                      cursor: 'pointer'
                    }}
                  >
                    🔧 Inicializar Voz
                  </button>
                )}
              </div>

              {voiceError && (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <div style={{
                    fontSize: '0.9rem',
                    color: '#ff6b6b',
                    textAlign: 'center'
                  }}>
                    ⚠️ {voiceError}
                  </div>
                  <button
                    onClick={restartVoiceRecognition}
                    style={{
                      padding: '0.5rem 1rem',
                      background: '#88FFD5',
                      color: '#001428',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '0.8rem',
                      fontWeight: '600',
                      cursor: 'pointer'
                    }}
                  >
                    🔄 Reintentar
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="menu-right">
          <div
            onClick={() => setIsLivesPopUpShown((prev) => !prev)}
            className="image-button"
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/lives.png"
              alt="Vidas"
              className="book-image"
            />

            {session && (
              <p
                className={`body2 bold questions-color-text ${questionsColorData.colorClass}`}
                style={{
                  color: questionsColorData.hexColor,
                }}
              >
                {questionsRemaining}
              </p>
            )}
          </div>

          <div
            onClick={handleShowClues}
            className={`image-button ${isClueButtonVibrating ? 'clues-button-vibrate' : ''}`}
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/clues.png"
              alt="Pistas"
              className="clues-image"
            />
            <p className="body2 bold">Pistas</p>
          </div>

          <div onClick={handleExistGame} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/exit.png"
              alt="Salir"
              className="exit-image"
            />
            <p className="body2 bold">Salir</p>
          </div>
        </div>

      </div>

      {/* Tooltip de pista - fuera del contenedor principal */}
      {showClueTooltip && (
        <div
          className={`clue-tooltip ${isClueFadingOut ? 'fade-out' : ''}`}
          style={{
            position: 'fixed',
            bottom: '2rem',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(136, 255, 213, 0.95)',
            color: '#001428',
            padding: '1.5rem 2rem',
            borderRadius: '16px',
            border: '2px solid #88FFD5',
            boxShadow: '0px 0px 20px 0px rgba(136, 255, 213, 0.6)',
            zIndex: 9999,
            maxWidth: '500px',
            textAlign: 'center',
            fontWeight: 600,
            fontSize: '1.1rem'
          }}
        >
          {currentClue}
        </div>
      )}

      {showExitPopup && (
        <Modal
          title="¿Seguro que quieres salir del juego?"
          onClose={handleCancelExit}
          onCancel={handleConfirmExit}
          onConfirm={handleCancelExit}
          cancelText="Salir de todos modos"
          confirmText="Seguir jugando"
          body="Si sales ahora, vas a perder tu progreso actual. Puedes seguir jugando o salir cuando quieras."
        />
      )}

      {isLivesPopUpShown && (
        <Modal
          title="Tus preguntas restantes"
          onClose={() => setIsLivesPopUpShown((prev) => !prev)}
          onConfirm={() => setIsLivesPopUpShown((prev) => !prev)}
          confirmText="Entendido"
          body=" Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada
          vez que haces una, se descuenta del contador. Piensa bien cada
          pregunta: ¡cada una cuenta!"
        />
      )}
    </>
  );
};

export default PlayView;
